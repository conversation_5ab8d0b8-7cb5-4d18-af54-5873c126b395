import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import '../models/auth_user_model.dart';
import '../../view_model/custom_exception.dart';
import '../../utils/getDeviceID.dart';

/// Result of a login operation
class LoginResult {
  final bool success;
  final String? twoFactorRefCode;
  final Map<String, String>? tokens;
  final String? error;

  const LoginResult({
    required this.success,
    this.twoFactorRefCode,
    this.tokens,
    this.error,
  });

  factory LoginResult.success(Map<String, String> tokens) {
    return LoginResult(success: true, tokens: tokens);
  }

  factory LoginResult.twoFactorRequired(String refCode) {
    return LoginResult(success: false, twoFactorRefCode: refCode);
  }

  factory LoginResult.failure(String error) {
    return LoginResult(success: false, error: error);
  }
}

/// Abstract interface for remote authentication data operations
abstract class AuthRemoteDataSource {
  /// Authentication operations
  Future<LoginResult> login(String email, String password);
  Future<void> logout();
  Future<Map<String, String>> verifyTwoFactor(String refCode, String code);
  Future<Map<String, String>> refreshToken(String refreshToken);

  /// User data operations
  Future<AuthUserModel> getUserInfo();

  /// Account management
  Future<void> updateUserInfo({
    required String oldPassword,
    required String fullName,
    required String email,
    required String phone,
    required String newPassword,
  });
}

/// Implementation of AuthRemoteDataSource using the existing API protocol
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final http.Client httpClient;

  // API endpoints
  static const String _au1Url = 'https://api.nudron.com/prod/au1';
  static const String _au3Url = 'https://api.nudron.com/prod/au3';

  // API constants
  static const String _tenantId = "d14b3819-5e90-4b1e-8821-9fcb72684627";
  static const String _clientId = "WaterMeteringMobile2";

  const AuthRemoteDataSourceImpl({
    required this.httpClient,
  });

  @override
  Future<LoginResult> login(String email, String password) async {
    try {
      final passwordBase64 = base64.encode(utf8.encode(password));
      final body = '02$email|$passwordBase64';

      final response = await _makeRequest(body, url: _au1Url);
      final splitResponse = response.split('|');

      if (response == '0') {
        return LoginResult.failure('Incorrect email or password');
      } else if (response == '10' || response == '01' || response == '00') {
        return LoginResult.failure('Email or phone unverified');
      } else if (splitResponse.length == 2) {
        // Login successful - tokens returned
        return LoginResult.success({
          'access_token': splitResponse[0],
          'refresh_token': splitResponse[1],
        });
      } else if (splitResponse.length == 1) {
        // Two-factor authentication required
        return LoginResult.twoFactorRequired(response);
      } else {
        return LoginResult.failure('Unexpected response');
      }
    } catch (e) {
      if (e is CustomException) {
        return LoginResult.failure(e.toString());
      }
      return LoginResult.failure('Login failed: ${e.toString()}');
    }
  }

  @override
  Future<void> logout() async {
    try {
      const body = '08';
      final response = await _makeRequest(body, url: _au3Url);

      if (response == '0') {
        throw CustomException('Error processing request');
      }
    } catch (e) {
      if (e is CustomException) rethrow;
      throw CustomException('Logout failed: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, String>> verifyTwoFactor(
      String refCode, String code) async {
    try {
      final body = '03$refCode|$code';
      final response = await _makeRequest(body, url: _au1Url);
      final splitResponse = response.split('|');

      if (response == '0') {
        throw CustomException('Incorrect code');
      } else if (response == '1') {
        throw CustomException('Code expired');
      } else if (splitResponse.length == 2) {
        return {
          'access_token': splitResponse[0],
          'refresh_token': splitResponse[1],
        };
      } else {
        throw CustomException('Unexpected response');
      }
    } catch (e) {
      if (e is CustomException) rethrow;
      throw CustomException('Two-factor verification failed: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, String>> refreshToken(String refreshToken) async {
    try {
      final body = '04$refreshToken';
      final response = await _makeRequest(body, url: _au1Url);
      final splitResponse = response.split('|');

      if (response == '0') {
        throw CustomException(
            'Redirecting to login page.. Please login again.');
      } else if (splitResponse.length == 2) {
        return {
          'access_token': splitResponse[0],
          'refresh_token': splitResponse[1],
        };
      } else {
        throw CustomException('Unexpected response');
      }
    } catch (e) {
      if (e is CustomException) rethrow;
      throw CustomException('Token refresh failed: ${e.toString()}');
    }
  }

  @override
  Future<AuthUserModel> getUserInfo() async {
    try {
      const body = '07';
      final response = await _makeRequest(body, url: _au3Url);
      final userMap = jsonDecode(response) as Map<String, dynamic>;

      return AuthUserModel.fromJson(userMap);
    } catch (e) {
      if (e is CustomException) rethrow;
      throw CustomException('Failed to get user info: ${e.toString()}');
    }
  }

  @override
  Future<void> updateUserInfo({
    required String oldPassword,
    required String fullName,
    required String email,
    required String phone,
    required String newPassword,
  }) async {
    try {
      final oldPassB64 = base64.encode(utf8.encode(oldPassword));
      final newPassB64 = base64.encode(utf8.encode(newPassword));

      final body = '00$oldPassB64|$fullName|$email|$phone|$newPassB64';
      final response = await _makeRequest(body, url: _au3Url);

      if (response == '0') {
        throw CustomException('Incorrect old password');
      } else if (response == '1') {
        throw CustomException('Email already in use');
      } else if (response == '2') {
        throw CustomException('Number already in use');
      }
    } catch (e) {
      if (e is CustomException) rethrow;
      throw CustomException('Failed to update user info: ${e.toString()}');
    }
  }

  /// Makes an HTTP request using the existing API protocol
  Future<String> _makeRequest(
    String body, {
    String url = _au1Url,
    Duration? timeout,
    String? accessToken,
  }) async {
    try {
      // Check connectivity
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        throw CustomException('No internet connection');
      }

      // Get user agent
      final userAgent = await DeviceInfoUtil.getUserAgent();

      // Prepare headers
      final headers = {
        // Only set User-Agent on non-web platforms (browsers don't allow it)
        if (!kIsWeb) 'User-Agent': userAgent,
        'medium': 'phone',
        'Content-Type': 'text/plain',
        if (accessToken != null) 'Authorization': 'Bearer $accessToken',
        if (url == _au1Url) 'tenantID': _tenantId,
        if (url == _au1Url) 'clientID': _clientId,
      };

      // Debug logging
      if (kDebugMode) {
        print('🔐 AUTH REQUEST DEBUG:');
        print('URL: $url');
        print('Body: $body');
        print('Headers: $headers');
      }

      // Make the request
      final response = await httpClient
          .post(
            Uri.parse(url),
            headers: headers,
            body: body,
          )
          .timeout(timeout ?? const Duration(seconds: 30));

      // Debug logging for response
      if (kDebugMode) {
        print('🔐 AUTH RESPONSE DEBUG:');
        print('Status Code: ${response.statusCode}');
        print('Response Body: ${response.body}');
        print('Response Headers: ${response.headers}');
      }

      // Handle HTTP errors
      if (response.statusCode != 200) {
        throw CustomException(
          'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        );
      }

      return response.body;
    } on SocketException {
      throw CustomException('No internet connection');
    } on HttpException catch (e) {
      throw CustomException('Network error: ${e.message}');
    } on FormatException {
      throw CustomException('Invalid response format');
    } catch (e) {
      if (e is CustomException) rethrow;
      throw CustomException('Request failed: ${e.toString()}');
    }
  }
}
